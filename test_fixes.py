#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
测试修复的逻辑
"""

# 模拟 trigger_trading_logic_wrapper 函数
async def trigger_trading_logic_wrapper(exchange_1_data, exchange_2_data):
    """触发交易逻辑的包装函数"""
    global trading_enabled
    
    if not trading_enabled:
        return
        
    try:
        print(f"接收到参数: exchange_1_data={exchange_1_data}, exchange_2_data={exchange_2_data}")
        await trigger_trading_logic()
    except Exception as e:
        print(f"交易逻辑执行错误: {e}")
        trading_enabled = False

async def trigger_trading_logic():
    """模拟交易逻辑"""
    print("执行交易逻辑...")

# 模拟持仓检查逻辑
def test_position_check():
    """测试持仓检查逻辑"""
    print("测试持仓检查逻辑...")
    
    # 模拟空持仓情况
    exchange_2_positions = []
    
    # 检查是否有持仓
    if not exchange_2_positions or len(exchange_2_positions) == 0:
        print('当前账户没有持仓，应该退出程序！')
        return False
        
    return True

# 模拟余额检查逻辑
def test_balance_check():
    """测试余额检查逻辑"""
    print("测试余额检查逻辑...")
    
    # 模拟余额不足情况
    current_balance = 0
    min_trade_amount = 10
    
    if current_balance <= 0:
        print(f'账户余额不足，当前余额: {current_balance}')
        return False
    
    # 计算实际可用余额
    max_execute_num = 100
    total_amount = 50
    available_balance = min(current_balance, max_execute_num - total_amount)
    
    if available_balance < min_trade_amount:
        print(f'可用余额不足，当前可用: {available_balance}, 最小交易量: {min_trade_amount}')
        return False
        
    return True

# 测试函数调用
import asyncio

async def main():
    global trading_enabled
    trading_enabled = True
    
    print("=== 测试函数参数修复 ===")
    # 测试函数能否正确接收参数
    await trigger_trading_logic_wrapper(
        {"bid": ["100", "10"], "ask": ["101", "10"]},
        {"bid": ["99", "10"], "ask": ["102", "10"]}
    )
    
    print("\n=== 测试持仓检查修复 ===")
    test_position_check()
    
    print("\n=== 测试余额检查修复 ===")
    test_balance_check()
    
    print("\n所有测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
